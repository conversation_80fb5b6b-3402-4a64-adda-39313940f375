```markdown
# Step-by-Step UI/UX Enhancement Plan for Switch.AI

---

## Step 1: Increase Top Bar Height & Remove Unnecessary Items

**File:** `src/components/TopBar.tsx`

```tsx
import { useAppStore } from '../store';

const modes = [
  { key: 'chat', label: 'Chat' },
  { key: 'create', label: 'Create' },
  { key: 'lab', label: 'Lab' },
  { key: 'hub', label: 'Hub' },
  { key: 'settings', label: 'Settings' }
] as const;

export const TopBar = () => {
  const { activeMode, setMode } = useAppStore();

  return (
    <div className="h-16 flex items-center justify-between px-6 border-b border-border bg-bgPrimary/80 backdrop-blur-xl">
      {/* Logo */}
      <div className="flex items-center space-x-3">
        <div className="w-8 h-8 bg-gradient-to-br from-accentStart to-accentEnd rounded-lg flex items-center justify-center">
          <span className="text-white font-bold text-sm">S</span>
        </div>
        <h1 className="text-xl font-bold bg-gradient-to-r from-accentStart to-accentEnd bg-clip-text text-transparent">
          Switch.AI
        </h1>
      </div>

      {/* Button Group Navigation */}
      <nav className="flex items-center bg-gray-100 dark:bg-bgSecondary p-1 rounded-lg border border-gray-200 dark:border-border">
        {modes.map((mode) => {
          const isActive = activeMode === mode.key;
          return (
            <button
              key={mode.key}
              onClick={() => setMode(mode.key)}
              className={`px-4 py-1.5 text-sm font-semibold rounded-md transition-all duration-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-accentStart focus-visible:ring-opacity-50 ${
                isActive
                  ? 'bg-white dark:bg-bgTertiary text-textPrimary shadow-sm'
                  : 'bg-transparent text-textSecondary hover:text-textPrimary'
              }`}
            >
              {mode.label}
            </button>
          );
        })}
      </nav>

      {/* Empty div to balance layout */}
      <div className="w-24" />
    </div>
  );
};
```

---

## Step 2: Unify Sidebar Icons & Improve Tooltips

**File:** `src/components/Sidebar.tsx`

```tsx
import { platforms } from '../data/platforms';
import { useAppStore } from '../store';
import { useState } from 'react';

export const Sidebar = () => {
  const { activeMode, activePlatform, setPlatform } = useAppStore();
  const [hoveredPlatform, setHoveredPlatform] = useState<string | null>(null);
  const list = platforms.filter((p) => p.mode === activeMode);

  return (
    <aside className="w-sidebar bg-bgSecondary border-r border-border flex flex-col items-center py-6 space-y-4 scrollbar-thin overflow-y-auto">
      {list.length === 0 ? (
        <div className="text-textTertiary text-xs text-center px-2">
          No platforms available
        </div>
      ) : (
        list.map((p) => {
          const isSelected = activePlatform?.id === p.id;
          const isHovered = hoveredPlatform === p.id;

          return (
            <div
              key={p.id}
              onClick={() => setPlatform(p)}
              onMouseEnter={() => setHoveredPlatform(p.id)}
              onMouseLeave={() => setHoveredPlatform(null)}
              className="group relative"
            >
              <div className={`platform-card ${isSelected ? 'selected' : ''}`}>
                {/* Unified Icon Background */}
                <div className={`absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800 opacity-20 group-hover:opacity-30 transition-opacity duration-300`} />

                {/* Icon */}
                <img
                  src={`/icons/${p.iconFile}`}
                  alt={p.name}
                  className={`w-8 h-8 relative z-10 transition-all duration-300 ${
                    isSelected ? 'drop-shadow-lg' : ''
                  } ${isHovered ? 'scale-110' : ''}`}
                />

                {/* Selection Indicator */}
                {isSelected && (
                  <div className="absolute -right-1.5 top-1/2 -translate-y-1/2 w-2 h-6 bg-accentStart rounded-full border-2 border-bgSecondary" />
                )}
              </div>

              {/* Bigger Tooltip */}
              <div className={`absolute left-full ml-4 top-1/2 -translate-y-1/2 bg-bgTertiary text-textPrimary text-base px-4 py-2 rounded-xl border border-border shadow-xl z-50 whitespace-nowrap transition-all duration-300 pointer-events-none ${
                isHovered ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-2'
              }`}>
                {p.name}
              </div>
            </div>
          );
        })
      )}
    </aside>
  );
};
```

---

## Step 3: Activate the WebView to Load Content

**File:** `src/components/WebView.tsx`

```tsx
import { useEffect, useState } from 'react';
import { useAppStore } from '../store';

export const WebView = () => {
  const { activePlatform } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);
  const [iframeKey, setIframeKey] = useState(0);

  useEffect(() => {
    if (activePlatform) {
      setIsLoading(true);
      setIframeKey((prev) => prev + 1);
      const timer = setTimeout(() => setIsLoading(false), 500);
      return () => clearTimeout(timer);
    }
  }, [activePlatform]);

  const EmptyState = () => (
    <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-bgPrimary to-bgTertiary">
      <div className="text-center space-y-6 max-w-md mx-auto px-6">
        <div className="w-24 h-24 mx-auto bg-gradient-to-br from-accentStart to-accentEnd rounded-3xl flex items-center justify-center shadow-xl">
          <span className="text-white text-2xl font-bold">AI</span>
        </div>
        <div className="space-y-3">
          <h3 className="text-2xl font-bold text-textPrimary">Select a Platform</h3>
          <p className="text-textSecondary leading-relaxed">
            Choose a platform from the sidebar to begin. Your selected web app will load here.
          </p>
        </div>
      </div>
    </div>
  );

  if (!activePlatform) return <EmptyState />;

  return (
    <main className="flex-1 bg-bgPrimary relative flex flex-col">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-bgPrimary z-20">
          <div className="w-16 h-16 border-4 border-accentStart border-t-transparent rounded-full animate-spin" />
        </div>
      )}
      <iframe
        key={iframeKey}
        src={activePlatform.url}
        title={activePlatform.name}
        className={`w-full h-full border-none transition-opacity duration-300 ${isLoading ? 'opacity-0' : 'opacity-100'}`}
        sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
      />
    </main>
  );
};
```

---

## Step 4: Add Tailwind-Based Button Group (Reusable)

**File:** `src/components/base/button-group/ButtonGroup.tsx`

```tsx
import React, { Children, cloneElement, isValidElement } from 'react';

type Props = {
  selectedKeys: Set<string>;
  onSelectionChange: (next: Set<string>) => void;
  children: React.ReactNode;
  className?: string;
};

export const ButtonGroup = ({ selectedKeys, onSelectionChange, children, className = '' }: Props) => {
  return (
    <div role="group" className={`inline-flex p-1 rounded-xl ${className}`}>
      {Children.map(children, (child) => {
        if (!isValidElement(child)) return child;
        const id = child.props.id;
        const selected = selectedKeys.has(id);
        return cloneElement(child, {
          selected,
          onSelect: () => {
            const next = new Set(selectedKeys);
            if (selected) {
              next.delete(id);
            } else {
              next.add(id);
            }
            onSelectionChange(next);
          },
        });
      })}
    </div>
  );
};
```

**File:** `src/components/base/button-group/ButtonGroupItem.tsx`

```tsx
import React from 'react';

type Props = {
  id: string;
  children: React.ReactNode;
  selected?: boolean;
  onSelect?: () => void;
};

export const ButtonGroupItem = ({ id, children, selected = false, onSelect }: Props) => {
  return (
    <button
      id={id}
      type="button"
      aria-pressed={selected}
      onClick={onSelect}
      className={`relative px-4 py-2 text-sm font-medium rounded-xl transition-all duration-200 ${
        selected
          ? 'text-white bg-gradient-to-r from-accentStart to-accentEnd shadow-lg'
          : 'text-textSecondary hover:text-textPrimary'
      }`}
    >
      <span>{children}</span>
    </button>
  );
};
```

**File:** `src/examples/SelectedItem.tsx`

```tsx
import { useState } from 'react';
import { ButtonGroup } from '@/components/base/button-group/ButtonGroup';
import { ButtonGroupItem } from '@/components/base/button-group/ButtonGroupItem';

export const SelectedItem = () => {
  const [selectedKeys, setSelectedKeys] = useState<Set<string>>(new Set(['today']));

  return (
    <ButtonGroup selectedKeys={selectedKeys} onSelectionChange={setSelectedKeys}>
      <ButtonGroupItem id="today">Today</ButtonGroupItem>
      <ButtonGroupItem id="tomorrow">Tomorrow</ButtonGroupItem>
      <ButtonGroupItem id="thisweek">This week</ButtonGroupItem>
    </ButtonGroup>
  );
};
```
```

